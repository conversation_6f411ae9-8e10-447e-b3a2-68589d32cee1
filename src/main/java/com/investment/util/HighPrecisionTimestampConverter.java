//package com.investment.util;
//
//import ch.qos.logback.classic.pattern.ClassicConverter;
//import ch.qos.logback.classic.spi.ILoggingEvent;
//
//import java.time.Instant;
//import java.time.ZoneId;
//import java.time.format.DateTimeFormatter;
//
//public class HighPrecisionTimestampConverter extends ClassicConverter {
//    private static final DateTimeFormatter FORMATTER =
//        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSSSSS")
//                         .withZone(ZoneId.systemDefault());
//
//    @Override
//    public String convert(ILoggingEvent event) {
//        // Use System.nanoTime() for high precision relative timing
//        long timestamp = event.getTimeStamp() * 1_000_000 + (System.nanoTime() % 1_000_000);
//        Instant instant = Instant.ofEpochMilli(event.getTimeStamp());
//        String baseTime = FORMATTER.format(instant);
//
//        // Replace the last 6 digits with more precise values
//        return baseTime.substring(0, 20) + String.format("%06d", timestamp % 1_000_000);
//    }
//}