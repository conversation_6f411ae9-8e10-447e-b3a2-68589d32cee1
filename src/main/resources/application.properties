# Server configuration
server.port=8080
server.servlet.context-path=/investment-toolkit

# Database configuration
spring.datasource.url=************************************
spring.datasource.username=sa
spring.datasource.password=

# Logging configuration
logging.level.root=INFO
logging.level.com.investment=DEBUG
logging.file.name=logs/investment-toolkit.log
#logging.pattern.console=%date{yyyy-MM-dd HH:mm:ss.SSSSSS} [%thread] %-5level %logger{36} - %msg%n
#logging.pattern.file=%date{yyyy-MM-dd HH:mm:ss.SSSSSS} [%thread] %-5level %logger{36} - %msg%n

# OpenAPI/Swagger configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
