<!--<configuration>-->
<!--    <conversionRule conversionWord="highPrecisionTimestamp"-->
<!--      converterClass="com.investment.util.HighPrecisionTimestampConverter" />-->

<!--    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">-->
<!--        &lt;!&ndash;-->
<!--        <encoder>-->
<!--            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>-->
<!--        </encoder>-->
<!--        &ndash;&gt;-->
<!--        &lt;!&ndash;-->
<!--        <encoder>-->
<!--            &lt;!&ndash; Microsecond precision with .SSSSSS &ndash;&gt;-->
<!--            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSSSSS} [%thread] %-5level %logger{36} - %msg%n</pattern>-->
<!--        </encoder>-->
<!--        &ndash;&gt;-->
<!--        <encoder>-->
<!--            &lt;!&ndash; Microsecond precision with .SSSSSSSSS &ndash;&gt;-->
<!--            <pattern>%highPrecisionTimestamp [%thread] %-5level %logger{36} - %msg%n</pattern>-->
<!--        </encoder>-->
<!--    </appender>-->
<!--    -->
<!--    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
<!--        <file>logs/investment-toolkit.log</file>-->
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">-->
<!--            <fileNamePattern>logs/investment-toolkit-%d{yyyy-MM-dd}.%i.log</fileNamePattern>-->
<!--            <maxFileSize>10MB</maxFileSize>-->
<!--            <maxHistory>30</maxHistory>-->
<!--            <totalSizeCap>1GB</totalSizeCap>-->
<!--        </rollingPolicy>-->
<!--        <encoder>-->
<!--            &lt;!&ndash;-->
<!--            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>-->
<!--            &ndash;&gt;-->
<!--            &lt;!&ndash; Microsecond precision with .SSSSSS &ndash;&gt;-->
<!--            &lt;!&ndash;-->
<!--            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSSSSS} [%thread] %-5level %logger{36} - %msg%n</pattern>-->
<!--            &ndash;&gt;-->
<!--            &lt;!&ndash; Microsecond precision with .SSSSSSSSS &ndash;&gt;-->
<!--            <pattern>%highPrecisionTimestamp [%thread] %-5level %logger{36} - %msg%n</pattern>-->
<!--        </encoder>-->
<!--    </appender>-->
<!--    -->
<!--    <root level="INFO">-->
<!--        <appender-ref ref="CONSOLE" />-->
<!--        <appender-ref ref="FILE" />-->
<!--    </root>-->
<!--</configuration>-->
