<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
  <Properties>
    <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.nnnnnnnnn} [%t] %-5level %logger{36} - %msg%n</Property>
  </Properties>
  <Appenders>
    <Console name="Console" target="SYSTEM_OUT">
      <PatternLayout pattern="${LOG_PATTERN}"/>
    </Console>
    <RollingFile name="File" fileName="logs/investment-toolkit.log"
      filePattern="logs/investment-toolkit-%d{yyyy-MM-dd}-%i.log">
      <PatternLayout pattern="${LOG_PATTERN}"/>
      <Policies>
        <SizeBasedTriggeringPolicy size="10MB"/>
        <TimeBasedTriggeringPolicy/>
      </Policies>
    </RollingFile>
  </Appenders>
  <Loggers>
    <Root level="info">
      <AppenderRef ref="Console"/>
      <AppenderRef ref="File"/>
    </Root>
    <Logger name="com.investment" level="debug" additivity="false">
      <AppenderRef ref="Console"/>
      <AppenderRef ref="File"/>
    </Logger>
  </Loggers>
</Configuration>