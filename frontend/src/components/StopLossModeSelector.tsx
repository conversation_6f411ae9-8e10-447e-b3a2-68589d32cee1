import React from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  ToggleButtonGroup,
  ToggleButton,
  useTheme,
  alpha,
  Fade,
  Grow,
} from '@mui/material';
import {
  TrendingUp as StandardIcon,
  Psychology as EnhancedIcon,
  Info as InfoIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';

export type StopLossMode = 'STANDARD' | 'ENHANCED';

interface StopLossModeSelectorProps {
  value: StopLossMode;
  onChange: (mode: StopLossMode) => void;
  disabled?: boolean;
  showDescription?: boolean;
  variant?: 'default' | 'compact' | 'card';
}

const StopLossModeSelector: React.FC<StopLossModeSelectorProps> = ({
  value,
  onChange,
  disabled = false,
  showDescription = true,
  variant = 'default',
}) => {
  const theme = useTheme();

  const handleChange = (
    event: React.MouseEvent<HTMLElement>,
    newValue: StopLossMode | null,
  ) => {
    if (newValue !== null) {
      onChange(newValue);
    }
  };

  const isCompact = variant === 'compact';
  const isCard = variant === 'card';

  return (
    <Box>
      <FormControl component="fieldset" disabled={disabled} fullWidth>
        <FormLabel component="legend" sx={{ mb: 1, fontWeight: 600 }}>
          Stop-Loss Calculation Mode
        </FormLabel>
        
        <RadioGroup
          value={value}
          onChange={handleChange}
          sx={{ gap: isCompact ? 0.5 : 1 }}
        >
          <FormControlLabel
            value="STANDARD"
            control={<Radio />}
            label={
              <Box display="flex" alignItems="center" gap={1}>
                <StandardIcon color="primary" fontSize="small" />
                <Box>
                  <Typography variant="body2" fontWeight={500}>
                    Standard Mode
                  </Typography>
                  {!isCompact && (
                    <Typography variant="caption" color="text.secondary">
                      Basic stop-loss calculation using fixed parameters
                    </Typography>
                  )}
                </Box>
                <Chip 
                  label="Default" 
                  size="small" 
                  color="primary" 
                  variant="outlined"
                  sx={{ ml: 'auto' }}
                />
              </Box>
            }
            sx={{ 
              alignItems: 'flex-start',
              '& .MuiFormControlLabel-label': { width: '100%' }
            }}
          />
          
          <FormControlLabel
            value="ENHANCED"
            control={<Radio />}
            label={
              <Box display="flex" alignItems="center" gap={1}>
                <EnhancedIcon color="secondary" fontSize="small" />
                <Box>
                  <Typography variant="body2" fontWeight={500}>
                    Enhanced Mode
                  </Typography>
                  {!isCompact && (
                    <Typography variant="caption" color="text.secondary">
                      Dynamic risk management with Bollinger Band analysis
                    </Typography>
                  )}
                </Box>
                <Tooltip title="Requires aggressive and conservative stop percentages to be configured">
                  <InfoIcon fontSize="small" color="action" sx={{ ml: 'auto' }} />
                </Tooltip>
              </Box>
            }
            sx={{ 
              alignItems: 'flex-start',
              '& .MuiFormControlLabel-label': { width: '100%' }
            }}
          />
        </RadioGroup>
      </FormControl>

      {showDescription && !isCompact && (
        <Box mt={2}>
          {value === 'STANDARD' && (
            <Alert severity="info" icon={<StandardIcon />}>
              <Typography variant="body2">
                <strong>Standard Mode:</strong> Uses basic stop-loss calculations with fixed parameters. 
                Effective stop value is determined by the higher of trailing stop and Bollinger Band middle band stop.
              </Typography>
            </Alert>
          )}
          
          {value === 'ENHANCED' && (
            <Alert severity="info" icon={<EnhancedIcon />}>
              <Typography variant="body2">
                <strong>Enhanced Mode:</strong> Uses dynamic risk management with position age analysis and 
                Bollinger Band trend detection. Automatically switches between aggressive and conservative 
                approaches based on market conditions.
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                <strong>Requirements:</strong> Positions must have both aggressive and conservative stop 
                percentages configured. Falls back to standard mode if parameters are missing.
              </Typography>
            </Alert>
          )}
        </Box>
      )}
    </Box>
  );
};

export default StopLossModeSelector;
