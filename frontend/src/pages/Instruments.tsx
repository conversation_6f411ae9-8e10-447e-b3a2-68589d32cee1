import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TableSortLabel,
  LinearProgress,
  ButtonBase,
  Tooltip,
  Checkbox,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  IconButton,
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Clear as ClearIcon,
  TrendingUp as TrendingUpIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  BookmarkAdd as BookmarkAddIcon,
  Sync as SyncIcon,
  Upload as UploadIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';

import { InstrumentService } from '../services/api/instrumentService';
import { OHLCVService } from '../services/api/ohlcvService';
import { WatchListService } from '../services/api/watchListService';
import { PositionService } from '../services/api/positionService';
import { Instrument, CreateInstrumentRequest, PaginatedResponse, BulkUpdateProgress, BulkUpdateResult, WatchListItem, CreateWatchListRequest, SyncResponse, CsvUploadResponse, Position } from '../types/api';
import BulkActionToolbar from '../components/BulkActionToolbar';
import BulkUpdateProgressDialog from '../components/BulkUpdateProgressDialog';

interface SortConfig {
  field: 'marketCap' | 'symbol' | 'name';
  direction: 'asc' | 'desc';
}

const Instruments: React.FC = () => {
  const navigate = useNavigate();

  // State management
  const [instruments, setInstruments] = useState<Instrument[]>([]);
  const [paginationData, setPaginationData] = useState<PaginatedResponse<Instrument> | null>(null);
  const [loading, setLoading] = useState(true);
  const [pageLoading, setPageLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Search and filtering
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchMode, setIsSearchMode] = useState(false);

  // Pagination and sorting
  const [currentPage, setCurrentPage] = useState(1); // 1-based for UI, 0-based for API
  const [pageSize, setPageSize] = useState(50);
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'marketCap',
    direction: 'desc'
  });

  // Bulk operations state
  const [selectedSymbols, setSelectedSymbols] = useState<Set<string>>(new Set());
  const [showBulkToolbar, setShowBulkToolbar] = useState(false);
  const [bulkUpdateProgress, setBulkUpdateProgress] = useState<BulkUpdateProgress[]>([]);
  const [showProgressDialog, setShowProgressDialog] = useState(false);
  const [bulkUpdateResult, setBulkUpdateResult] = useState<BulkUpdateResult | null>(null);
  const [isBulkUpdateComplete, setIsBulkUpdateComplete] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Abort controller for cancelling bulk operations
  const abortControllerRef = useRef<AbortController | null>(null);

  // Add instrument dialog state
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [addInstrumentForm, setAddInstrumentForm] = useState<CreateInstrumentRequest>({
    symbol: '',
    name: '',
    type: 'US_STOCK',
    marketCap: undefined,
    country: '',
    ipoYear: undefined,
    sector: '',
    industry: ''
  });
  const [addInstrumentLoading, setAddInstrumentLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Edit instrument dialog state
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingInstrument, setEditingInstrument] = useState<Instrument | null>(null);
  const [editInstrumentForm, setEditInstrumentForm] = useState<CreateInstrumentRequest>({
    symbol: '',
    name: '',
    type: 'US_STOCK',
    marketCap: undefined,
    country: '',
    ipoYear: undefined,
    sector: '',
    industry: ''
  });
  const [editInstrumentLoading, setEditInstrumentLoading] = useState(false);

  // Delete confirmation dialog state
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletingInstrument, setDeletingInstrument] = useState<Instrument | null>(null);
  const [deleteInstrumentLoading, setDeleteInstrumentLoading] = useState(false);

  // Watch list functionality state
  const [watchListItems, setWatchListItems] = useState<WatchListItem[]>([]);
  const [addingToWatchList, setAddingToWatchList] = useState<Set<string>>(new Set());
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'success'
  });

  // Sync US Stock Data state
  const [syncLoading, setSyncLoading] = useState(false);
  const [showSyncConfirmDialog, setShowSyncConfirmDialog] = useState(false);

  // CSV Upload state
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showCsvUploadDialog, setShowCsvUploadDialog] = useState(false);
  const [csvUploadLoading, setCsvUploadLoading] = useState(false);
  const [csvUploadResult, setCsvUploadResult] = useState<CsvUploadResponse | null>(null);
  const [showCsvResultDialog, setShowCsvResultDialog] = useState(false);

  // Load instruments with current pagination and sorting
  const loadInstruments = useCallback(async (showPageLoading = false) => {
    try {
      if (showPageLoading) {
        setPageLoading(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const apiPage = currentPage - 1; // Convert to 0-based for API
      const response = await InstrumentService.getInstruments(
        apiPage,
        pageSize,
        sortConfig.field,
        sortConfig.direction
      );

      if (response.success && response.data) {
        setInstruments(response.data.content);
        setPaginationData(response.data);
        setIsSearchMode(false);
      } else {
        setError(response.message || 'Failed to load instruments');
      }
    } catch (err: any) {
      console.error('Error loading instruments:', err);
      setError('Failed to load instruments');
    } finally {
      setLoading(false);
      setPageLoading(false);
    }
  }, [currentPage, pageSize, sortConfig]);

  // Initial load
  useEffect(() => {
    loadInstruments();
  }, [loadInstruments]);

  // Search functionality
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      // Clear search and return to paginated view
      setIsSearchMode(false);
      setCurrentPage(1);
      loadInstruments();
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await InstrumentService.searchInstruments(searchQuery);
      if (response.success && response.data) {
        setInstruments(response.data);
        setIsSearchMode(true);
        setPaginationData(null); // Clear pagination for search results
      } else {
        setError(response.message || 'Failed to search instruments');
      }
    } catch (err: any) {
      console.error('Error searching instruments:', err);
      setError('Failed to search instruments');
    } finally {
      setLoading(false);
    }
  };

  // Clear search and return to paginated view
  const handleClearSearch = () => {
    setSearchQuery('');
    setIsSearchMode(false);
    setCurrentPage(1);
    // Clear selections when switching between search and pagination modes
    setSelectedSymbols(new Set());
    setShowBulkToolbar(false);
    loadInstruments();
  };

  // Handle page change
  const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
    setCurrentPage(page);
    loadInstruments(true); // Show page loading indicator
    // Note: selectedSymbols state is preserved across page changes
  };

  // Handle page size change
  const handlePageSizeChange = (event: any) => {
    const newSize = parseInt(event.target.value);
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page
  };

  // Handle sorting
  const handleSort = (field: 'marketCap' | 'symbol' | 'name') => {
    const newDirection = sortConfig.field === field && sortConfig.direction === 'desc' ? 'asc' : 'desc';
    setSortConfig({ field, direction: newDirection });
    setCurrentPage(1); // Reset to first page when sorting changes
    // Note: selectedSymbols state is preserved across sorting changes
  };

  // Handle navigation to OHLCV page
  const handleInstrumentClick = (symbol: string) => {
    try {
      // Navigate to OHLCV page with symbol parameter
      navigate(`/ohlcv/${symbol.toUpperCase()}`);
    } catch (error) {
      console.error('Navigation error:', error);
      setError(`Failed to navigate to OHLCV data for ${symbol}`);
    }
  };

  // Bulk selection handlers
  const handleSelectSymbol = (symbol: string, checked: boolean) => {
    const newSelection = new Set(selectedSymbols);
    if (checked) {
      newSelection.add(symbol);
    } else {
      newSelection.delete(symbol);
    }
    setSelectedSymbols(newSelection);
    setShowBulkToolbar(newSelection.size > 0);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allSymbols = new Set(instruments.map(instrument => instrument.symbol));
      setSelectedSymbols(allSymbols);
      setShowBulkToolbar(true);
    } else {
      setSelectedSymbols(new Set());
      setShowBulkToolbar(false);
    }
  };

  const handleClearSelection = () => {
    setSelectedSymbols(new Set());
    setShowBulkToolbar(false);
  };

  // Bulk OHLCV update handlers
  const handleBulkUpdateOHLCV = async () => {
    if (selectedSymbols.size === 0) return;

    const symbolsArray = Array.from(selectedSymbols);
    setBulkUpdateProgress(symbolsArray.map(symbol => ({ symbol, status: 'pending' })));
    setShowProgressDialog(true);
    setIsBulkUpdateComplete(false);
    setBulkUpdateResult(null);

    // Create abort controller for cancellation
    abortControllerRef.current = new AbortController();

    try {
      const result = await OHLCVService.bulkUpdateOHLCVData(
        { symbols: symbolsArray, dryRun: false },
        (progress) => setBulkUpdateProgress(progress),
        abortControllerRef.current.signal
      );

      setBulkUpdateResult(result);
      setIsBulkUpdateComplete(true);

      if (result.errorCount === 0) {
        setSuccessMessage(`Successfully updated OHLCV data for ${result.successCount} symbols`);
        handleClearSelection(); // Clear selection on complete success
      }
    } catch (error: any) {
      console.error('Bulk update error:', error);
      setError('Bulk update operation failed');
      setIsBulkUpdateComplete(true);
    }
  };

  const handleCancelBulkUpdate = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  };

  const handleRetryFailedSymbols = async () => {
    if (!bulkUpdateResult) return;

    const failedSymbols = bulkUpdateProgress.filter(p => p.status === 'error').map(p => p.symbol);
    if (failedSymbols.length === 0) return;

    setBulkUpdateProgress(failedSymbols.map(symbol => ({ symbol, status: 'pending' })));
    setIsBulkUpdateComplete(false);
    setBulkUpdateResult(null);

    abortControllerRef.current = new AbortController();

    try {
      const result = await OHLCVService.retryFailedSymbols(
        bulkUpdateProgress,
        { dryRun: false },
        (progress) => setBulkUpdateProgress(progress),
        abortControllerRef.current.signal
      );

      setBulkUpdateResult(result);
      setIsBulkUpdateComplete(true);

      if (result.errorCount === 0) {
        setSuccessMessage(`Successfully retried and updated ${result.successCount} symbols`);
      }
    } catch (error: any) {
      console.error('Retry failed symbols error:', error);
      setError('Retry operation failed');
      setIsBulkUpdateComplete(true);
    }
  };

  const handleCloseProgressDialog = () => {
    setShowProgressDialog(false);
    setBulkUpdateProgress([]);
    setBulkUpdateResult(null);
    setIsBulkUpdateComplete(false);
  };

  // Add instrument handlers
  const handleOpenAddDialog = () => {
    setShowAddDialog(true);
    setFormErrors({});
  };

  const handleCloseAddDialog = () => {
    setShowAddDialog(false);
    setAddInstrumentForm({
      symbol: '',
      name: '',
      type: 'US_STOCK',
      marketCap: undefined,
      country: '',
      ipoYear: undefined,
      sector: '',
      industry: ''
    });
    setFormErrors({});
  };

  const handleFormChange = (field: keyof CreateInstrumentRequest, value: any) => {
    setAddInstrumentForm(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error for this field when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!addInstrumentForm.symbol.trim()) {
      errors.symbol = 'Symbol is required';
    } else if (!/^[A-Z0-9.-]+$/.test(addInstrumentForm.symbol.trim().toUpperCase())) {
      errors.symbol = 'Symbol must contain only uppercase letters, numbers, dots, and hyphens';
    }

    if (!addInstrumentForm.name.trim()) {
      errors.name = 'Company name is required';
    }

    if (addInstrumentForm.marketCap !== undefined && addInstrumentForm.marketCap < 0) {
      errors.marketCap = 'Market cap must be non-negative';
    }

    if (addInstrumentForm.ipoYear !== undefined) {
      const currentYear = new Date().getFullYear();
      if (addInstrumentForm.ipoYear < 1800 || addInstrumentForm.ipoYear > currentYear) {
        errors.ipoYear = `IPO year must be between 1800 and ${currentYear}`;
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateInstrument = async () => {
    if (!validateForm()) {
      return;
    }

    setAddInstrumentLoading(true);
    try {
      // Prepare the request with cleaned data
      const request: CreateInstrumentRequest = {
        symbol: addInstrumentForm.symbol.trim().toUpperCase(),
        name: addInstrumentForm.name.trim(),
        type: addInstrumentForm.type,
        marketCap: addInstrumentForm.marketCap || undefined,
        country: addInstrumentForm.country?.trim() || undefined,
        ipoYear: addInstrumentForm.ipoYear || undefined,
        sector: addInstrumentForm.sector?.trim() || undefined,
        industry: addInstrumentForm.industry?.trim() || undefined
      };

      const response = await InstrumentService.createInstrument(request);

      if (response.success) {
        setSuccessMessage(`Successfully created instrument: ${request.symbol}`);
        handleCloseAddDialog();
        // Refresh the instruments list
        loadInstruments();
        // Set flag to refresh instruments in other pages (like Technical Indicators)
        localStorage.setItem('refreshInstruments', 'true');
      } else {
        setError(response.message || 'Failed to create instrument');
      }
    } catch (err: any) {
      console.error('Error creating instrument:', err);
      if (err.message?.includes('already exists')) {
        setFormErrors({ symbol: 'Symbol already exists' });
      } else {
        setError('Failed to create instrument: ' + (err.message || 'Unknown error'));
      }
    } finally {
      setAddInstrumentLoading(false);
    }
  };

  // Edit instrument handlers
  const handleEditInstrument = (instrument: Instrument) => {
    setEditingInstrument(instrument);
    setEditInstrumentForm({
      symbol: instrument.symbol,
      name: instrument.name,
      type: instrument.type,
      marketCap: instrument.marketCap || undefined,
      country: instrument.country || '',
      ipoYear: instrument.ipoYear || undefined,
      sector: instrument.sector || '',
      industry: instrument.industry || ''
    });
    setShowEditDialog(true);
    setFormErrors({});
  };

  const handleCloseEditDialog = () => {
    setShowEditDialog(false);
    setEditingInstrument(null);
    setEditInstrumentForm({
      symbol: '',
      name: '',
      type: 'US_STOCK',
      marketCap: undefined,
      country: '',
      ipoYear: undefined,
      sector: '',
      industry: ''
    });
    setFormErrors({});
  };

  const handleEditFormChange = (field: keyof CreateInstrumentRequest, value: any) => {
    setEditInstrumentForm(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error for this field when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateEditForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!editInstrumentForm.symbol.trim()) {
      errors.symbol = 'Symbol is required';
    } else if (!/^[A-Z0-9.-]+$/.test(editInstrumentForm.symbol.trim().toUpperCase())) {
      errors.symbol = 'Symbol must contain only uppercase letters, numbers, dots, and hyphens';
    }

    if (!editInstrumentForm.name.trim()) {
      errors.name = 'Company name is required';
    }

    if (editInstrumentForm.marketCap !== undefined && editInstrumentForm.marketCap < 0) {
      errors.marketCap = 'Market cap must be non-negative';
    }

    if (editInstrumentForm.ipoYear !== undefined) {
      const currentYear = new Date().getFullYear();
      if (editInstrumentForm.ipoYear < 1800 || editInstrumentForm.ipoYear > currentYear) {
        errors.ipoYear = `IPO year must be between 1800 and ${currentYear}`;
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleUpdateInstrument = async () => {
    if (!validateEditForm() || !editingInstrument) {
      return;
    }

    setEditInstrumentLoading(true);
    try {
      // Prepare the request with cleaned data
      const request: CreateInstrumentRequest = {
        symbol: editInstrumentForm.symbol.trim().toUpperCase(),
        name: editInstrumentForm.name.trim(),
        type: editInstrumentForm.type,
        marketCap: editInstrumentForm.marketCap || undefined,
        country: editInstrumentForm.country?.trim() || undefined,
        ipoYear: editInstrumentForm.ipoYear || undefined,
        sector: editInstrumentForm.sector?.trim() || undefined,
        industry: editInstrumentForm.industry?.trim() || undefined
      };

      const response = await InstrumentService.updateInstrument(editingInstrument.symbol, request);

      if (response.success) {
        setSuccessMessage(`Successfully updated instrument: ${request.symbol}`);
        handleCloseEditDialog();
        // Refresh the instruments list
        loadInstruments();
        // Set flag to refresh instruments in other pages
        localStorage.setItem('refreshInstruments', 'true');
      } else {
        setError(response.message || 'Failed to update instrument');
      }
    } catch (err: any) {
      console.error('Error updating instrument:', err);
      if (err.message?.includes('already exists')) {
        setFormErrors({ symbol: 'Symbol already exists' });
      } else {
        setError('Failed to update instrument: ' + (err.message || 'Unknown error'));
      }
    } finally {
      setEditInstrumentLoading(false);
    }
  };

  // Delete instrument handlers
  const handleDeleteInstrument = (instrument: Instrument) => {
    setDeletingInstrument(instrument);
    setShowDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setShowDeleteDialog(false);
    setDeletingInstrument(null);
  };

  const handleConfirmDelete = async () => {
    if (!deletingInstrument) return;

    setDeleteInstrumentLoading(true);
    try {
      const response = await InstrumentService.deleteInstrument(deletingInstrument.symbol);

      if (response.success) {
        setSuccessMessage(`Successfully deleted instrument: ${deletingInstrument.symbol}`);
        handleCloseDeleteDialog();
        // Refresh the instruments list
        loadInstruments();
        // Clear selection if deleted instrument was selected
        if (selectedSymbols.has(deletingInstrument.symbol)) {
          const newSelection = new Set(selectedSymbols);
          newSelection.delete(deletingInstrument.symbol);
          setSelectedSymbols(newSelection);
          setShowBulkToolbar(newSelection.size > 0);
        }
        // Set flag to refresh instruments in other pages
        localStorage.setItem('refreshInstruments', 'true');
      } else {
        setError(response.message || 'Failed to delete instrument');
      }
    } catch (err: any) {
      console.error('Error deleting instrument:', err);
      setError('Failed to delete instrument: ' + (err.message || 'Unknown error'));
    } finally {
      setDeleteInstrumentLoading(false);
    }
  };

  // Watch list functionality
  const loadWatchListItems = useCallback(async () => {
    try {
      const response = await WatchListService.getWatchListItems();
      if (response.success && response.data) {
        setWatchListItems(response.data);
      }
    } catch (err: any) {
      console.error('Error loading watch list items:', err);
    }
  }, []);

  // Load watch list items on component mount
  useEffect(() => {
    loadWatchListItems();
  }, [loadWatchListItems]);

  const handleAddToWatchList = async (symbol: string) => {
    // Check if symbol is already in watch list
    const existingItem = watchListItems.find(item => item.symbol.toUpperCase() === symbol.toUpperCase());
    if (existingItem) {
      setSnackbar({
        open: true,
        message: `${symbol} is already in your watch list`,
        severity: 'warning'
      });
      return;
    }

    // Set loading state for this specific symbol
    setAddingToWatchList(prev => new Set(prev).add(symbol));

    try {
      // Calculate next display index
      const nextDisplayIndex = watchListItems.length > 0
        ? Math.max(...watchListItems.map(item => item.displayIndex)) + 1
        : 1;

      const request: CreateWatchListRequest = {
        displayIndex: nextDisplayIndex,
        symbol: symbol.toUpperCase(),
        startDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
        remarks: `Added from Instruments page`
      };

      const response = await WatchListService.createWatchListItem(request);

      if (response.success) {
        // Refresh watch list items
        await loadWatchListItems();
        setSnackbar({
          open: true,
          message: `Successfully added ${symbol} to watch list`,
          severity: 'success'
        });
      } else {
        setSnackbar({
          open: true,
          message: response.message || `Failed to add ${symbol} to watch list`,
          severity: 'error'
        });
      }
    } catch (err: any) {
      console.error('Error adding to watch list:', err);
      setSnackbar({
        open: true,
        message: `Failed to add ${symbol} to watch list: ${err.message || 'Unknown error'}`,
        severity: 'error'
      });
    } finally {
      // Remove loading state for this symbol
      setAddingToWatchList(prev => {
        const newSet = new Set(prev);
        newSet.delete(symbol);
        return newSet;
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Sync US Stock Data handlers
  const handleOpenSyncDialog = () => {
    setShowSyncConfirmDialog(true);
  };

  const handleCloseSyncDialog = () => {
    setShowSyncConfirmDialog(false);
  };

  const handleSyncUSStockData = async () => {
    try {
      setSyncLoading(true);
      setShowSyncConfirmDialog(false);
      setError(null);

      const response = await InstrumentService.syncWithSec(false, false, 1000);

      if (response.success && response.data) {
        const syncData = response.data;
        setSnackbar({
          open: true,
          message: `Sync completed successfully! Added ${syncData.addedSymbols} new instruments from ${syncData.totalSecSymbols} SEC symbols.`,
          severity: 'success'
        });

        // Refresh the instruments list to show newly added instruments
        loadInstruments();

        // Set flag to refresh instruments in other pages
        localStorage.setItem('refreshInstruments', 'true');
      } else {
        setError(response.message || 'Failed to sync US stock data');
      }
    } catch (err: any) {
      console.error('Error syncing US stock data:', err);
      setError('Failed to sync US stock data: ' + (err.message || 'Unknown error'));
    } finally {
      setSyncLoading(false);
    }
  };

  // CSV Upload handlers
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.name.toLowerCase().endsWith('.csv')) {
        setSnackbar({
          open: true,
          message: 'Please select a CSV file',
          severity: 'error'
        });
        return;
      }

      // Validate file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        setSnackbar({
          open: true,
          message: 'File size must be less than 10MB',
          severity: 'error'
        });
        return;
      }

      setSelectedFile(file);
      setShowCsvUploadDialog(true);
    }
  };

  const handleCsvUpload = async () => {
    if (!selectedFile) return;

    try {
      setCsvUploadLoading(true);
      setShowCsvUploadDialog(false);
      setError(null);

      const response = await InstrumentService.uploadCsv(
        selectedFile,
        false, // dryRun = false for actual upload
        10000,  // maxInstruments
        true,  // skipDuplicates
        true   // validateData
      );

      if (response.success && response.data) {
        setCsvUploadResult(response.data);
        setShowCsvResultDialog(true);

        // Show success message
        setSnackbar({
          open: true,
          message: `CSV upload completed! ${response.data.summary}`,
          severity: response.data.addedInstruments > 0 || response.data.updatedInstruments > 0 ? 'success' : 'info'
        });

        // Refresh the instruments list to show updated data
        loadInstruments();

        // Set flag to refresh instruments in other pages
        localStorage.setItem('refreshInstruments', 'true');
      } else {
        setError(response.message || 'Failed to upload CSV file');
      }
    } catch (err: any) {
      console.error('Error uploading CSV file:', err);
      setError('Failed to upload CSV file: ' + (err.message || 'Unknown error'));
    } finally {
      setCsvUploadLoading(false);
      setSelectedFile(null);
      // Reset file input
      const fileInput = document.getElementById('csv-file-input') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
    }
  };

  const handleCloseCsvUploadDialog = () => {
    setShowCsvUploadDialog(false);
    setSelectedFile(null);
    // Reset file input
    const fileInput = document.getElementById('csv-file-input') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
  };

  const handleCloseCsvResultDialog = () => {
    setShowCsvResultDialog(false);
    setCsvUploadResult(null);
  };

  // Utility functions
  const formatMarketCap = (marketCap?: number) => {
    if (!marketCap) return 'N/A';

    if (marketCap >= 1e12) {
      return `$${(marketCap / 1e12).toFixed(2)}T`;
    } else if (marketCap >= 1e9) {
      return `$${(marketCap / 1e9).toFixed(2)}B`;
    } else if (marketCap >= 1e6) {
      return `$${(marketCap / 1e6).toFixed(2)}M`;
    } else {
      return `$${marketCap.toLocaleString()}`;
    }
  };

  const getPaginationInfo = () => {
    if (isSearchMode) {
      return `Showing ${instruments.length} search results`;
    }
    if (!paginationData) return '';

    const start = (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, paginationData.totalElements);
    return `Showing ${start}-${end} of ${paginationData.totalElements.toLocaleString()} instruments`;
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Financial Instruments
      </Typography>

      {/* Search and Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            {/* Search Field */}
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Search instruments"
                placeholder="Enter symbol or company name"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>

            {/* Search Actions */}
            <Grid item xs={12} md={2}>
              <Button
                variant="contained"
                onClick={handleSearch}
                fullWidth
                startIcon={<SearchIcon />}
                disabled={loading || pageLoading}
              >
                Search
              </Button>
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                onClick={handleClearSearch}
                fullWidth
                startIcon={<ClearIcon />}
                disabled={loading || pageLoading}
              >
                Clear
              </Button>
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                onClick={() => loadInstruments()}
                fullWidth
                startIcon={<RefreshIcon />}
                disabled={loading || pageLoading}
              >
                Refresh
              </Button>
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                variant="contained"
                color="success"
                onClick={handleOpenAddDialog}
                fullWidth
                startIcon={<AddIcon />}
                disabled={loading || pageLoading}
              >
                Add New
              </Button>
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleOpenSyncDialog}
                fullWidth
                startIcon={syncLoading ? <CircularProgress size={16} color="inherit" /> : <SyncIcon />}
                disabled={loading || pageLoading || syncLoading}
              >
                {syncLoading ? 'Syncing...' : 'Sync US Stock Data'}
              </Button>
            </Grid>

            <Grid item xs={12} md={2}>
              <input
                id="csv-file-input"
                type="file"
                accept=".csv"
                style={{ display: 'none' }}
                onChange={handleFileSelect}
              />
              <Button
                variant="contained"
                color="secondary"
                onClick={() => document.getElementById('csv-file-input')?.click()}
                fullWidth
                startIcon={csvUploadLoading ? <CircularProgress size={16} color="inherit" /> : <CloudUploadIcon />}
                disabled={loading || pageLoading || csvUploadLoading}
              >
                {csvUploadLoading ? 'Uploading...' : 'Upload CSV'}
              </Button>
            </Grid>

            {/* Page Size Selector */}
            {!isSearchMode && (
              <Grid item xs={12} md={1}>
                <FormControl fullWidth size="small">
                  <InputLabel>Size</InputLabel>
                  <Select
                    value={pageSize}
                    label="Size"
                    onChange={handlePageSizeChange}
                    disabled={loading || pageLoading}
                  >
                    <MenuItem value={25}>25</MenuItem>
                    <MenuItem value={50}>50</MenuItem>
                    <MenuItem value={100}>100</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
          </Grid>

          {/* Pagination Info */}
          <Box mt={2} display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="body2" color="text.secondary">
              {getPaginationInfo()}
            </Typography>
            {(loading || pageLoading) && (
              <Typography variant="body2" color="primary">
                {pageLoading ? 'Loading page...' : 'Loading...'}
              </Typography>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Page Loading Indicator */}
      {pageLoading && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress />
        </Box>
      )}

      {/* Main Loading */}
      {loading && !pageLoading && (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      )}

      {/* Instruments Table */}
      {!loading && (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      indeterminate={selectedSymbols.size > 0 && selectedSymbols.size < instruments.length}
                      checked={instruments.length > 0 && selectedSymbols.size === instruments.length}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      disabled={instruments.length === 0}
                    />
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortConfig.field === 'symbol'}
                      direction={sortConfig.field === 'symbol' ? sortConfig.direction : 'asc'}
                      onClick={() => handleSort('symbol')}
                      disabled={isSearchMode}
                    >
                      <strong>Symbol</strong>
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortConfig.field === 'name'}
                      direction={sortConfig.field === 'name' ? sortConfig.direction : 'asc'}
                      onClick={() => handleSort('name')}
                      disabled={isSearchMode}
                    >
                      <strong>Company Name</strong>
                    </TableSortLabel>
                  </TableCell>
                  <TableCell><strong>Type</strong></TableCell>
                  <TableCell><strong>Sector</strong></TableCell>
                  <TableCell><strong>Industry</strong></TableCell>
                  <TableCell align="right">
                    <TableSortLabel
                      active={sortConfig.field === 'marketCap'}
                      direction={sortConfig.field === 'marketCap' ? sortConfig.direction : 'desc'}
                      onClick={() => handleSort('marketCap')}
                      disabled={isSearchMode}
                    >
                      <strong>Market Cap</strong>
                    </TableSortLabel>
                  </TableCell>
                  <TableCell><strong>Country</strong></TableCell>
                  <TableCell align="center"><strong>Actions</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {instruments.map((instrument) => (
                  <TableRow
                    key={instrument.symbol}
                    hover
                    selected={selectedSymbols.has(instrument.symbol)}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedSymbols.has(instrument.symbol)}
                        onChange={(e) => handleSelectSymbol(instrument.symbol, e.target.checked)}
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip title={`View OHLCV data for ${instrument.symbol}`} arrow>
                        <ButtonBase
                          onClick={() => handleInstrumentClick(instrument.symbol)}
                          sx={{
                            borderRadius: 1,
                            transition: 'all 0.2s ease-in-out',
                            '&:hover': {
                              transform: 'scale(1.05)',
                            },
                          }}
                        >
                          <Chip
                            label={instrument.symbol}
                            variant="outlined"
                            size="small"
                            clickable
                            icon={<TrendingUpIcon />}
                            sx={{
                              fontWeight: 600,
                              cursor: 'pointer',
                              '&:hover': {
                                backgroundColor: 'primary.light',
                                color: 'primary.contrastText',
                                borderColor: 'primary.main',
                              },
                            }}
                          />
                        </ButtonBase>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Tooltip title={`View OHLCV data for ${instrument.symbol}`} arrow>
                        <ButtonBase
                          onClick={() => handleInstrumentClick(instrument.symbol)}
                          sx={{
                            textAlign: 'left',
                            borderRadius: 1,
                            padding: '4px 8px',
                            transition: 'all 0.2s ease-in-out',
                            '&:hover': {
                              backgroundColor: 'action.hover',
                              transform: 'translateX(4px)',
                            },
                          }}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              cursor: 'pointer',
                              color: 'primary.main',
                              fontWeight: 500,
                              '&:hover': {
                                textDecoration: 'underline',
                              },
                            }}
                          >
                            {instrument.name}
                          </Typography>
                        </ButtonBase>
                      </Tooltip>
                    </TableCell>
                    <TableCell>{instrument.type}</TableCell>
                    <TableCell>{instrument.sector || 'N/A'}</TableCell>
                    <TableCell>{instrument.industry || 'N/A'}</TableCell>
                    <TableCell align="right">
                      {formatMarketCap(instrument.marketCap)}
                    </TableCell>
                    <TableCell>
                      {instrument.country || 'N/A'}
                    </TableCell>
                    <TableCell align="center">
                      <Box display="flex" gap={1} justifyContent="center">
                        <Tooltip title={`Add ${instrument.symbol} to watch list`} arrow>
                          <IconButton
                            size="small"
                            onClick={() => handleAddToWatchList(instrument.symbol)}
                            color="secondary"
                            disabled={addingToWatchList.has(instrument.symbol)}
                          >
                            {addingToWatchList.has(instrument.symbol) ? (
                              <CircularProgress size={16} />
                            ) : (
                              <BookmarkAddIcon />
                            )}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit instrument" arrow>
                          <IconButton
                            size="small"
                            onClick={() => handleEditInstrument(instrument)}
                            color="primary"
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete instrument" arrow>
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteInstrument(instrument)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination Controls */}
          {!isSearchMode && paginationData && paginationData.totalPages > 1 && (
            <Box
              display="flex"
              justifyContent="center"
              alignItems="center"
              p={2}
              borderTop="1px solid"
              borderColor="divider"
            >
              <Pagination
                count={paginationData.totalPages}
                page={currentPage}
                onChange={handlePageChange}
                color="primary"
                size="large"
                showFirstButton
                showLastButton
                disabled={pageLoading}
              />
            </Box>
          )}
        </Paper>
      )}

      {/* No Results */}
      {!loading && instruments.length === 0 && (
        <Paper>
          <Box textAlign="center" p={4}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {isSearchMode ? 'No search results found' : 'No instruments found'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {isSearchMode
                ? 'Try adjusting your search criteria or clear the search to view all instruments'
                : 'Try refreshing the data or check your connection'
              }
            </Typography>
            {isSearchMode && (
              <Button
                variant="outlined"
                onClick={handleClearSearch}
                sx={{ mt: 2 }}
                startIcon={<ClearIcon />}
              >
                Clear Search
              </Button>
            )}
          </Box>
        </Paper>
      )}

      {/* Bulk Action Toolbar */}
      <BulkActionToolbar
        selectedCount={selectedSymbols.size}
        onUpdateOHLCV={handleBulkUpdateOHLCV}
        onClearSelection={handleClearSelection}
        onClose={() => setShowBulkToolbar(false)}
        isVisible={showBulkToolbar}
        isLoading={showProgressDialog && !isBulkUpdateComplete}
      />

      {/* Bulk Update Progress Dialog */}
      <BulkUpdateProgressDialog
        open={showProgressDialog}
        onClose={handleCloseProgressDialog}
        onCancel={handleCancelBulkUpdate}
        onRetryFailed={handleRetryFailedSymbols}
        progress={bulkUpdateProgress}
        isComplete={isBulkUpdateComplete}
        result={bulkUpdateResult || undefined}
        title="Bulk OHLCV Data Update"
      />

      {/* Success Snackbar */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={() => setSuccessMessage(null)}
        message={successMessage}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      />

      {/* Add Instrument Dialog */}
      <Dialog
        open={showAddDialog}
        onClose={handleCloseAddDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Add New Financial Instrument
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              {/* Required Fields */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Required Information
                </Typography>
                <Divider sx={{ mb: 2 }} />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Symbol"
                  placeholder="e.g., AAPL"
                  value={addInstrumentForm.symbol}
                  onChange={(e) => handleFormChange('symbol', e.target.value.toUpperCase())}
                  error={!!formErrors.symbol}
                  helperText={formErrors.symbol || 'Unique instrument symbol (uppercase letters, numbers, dots, hyphens)'}
                  required
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Instrument Type</InputLabel>
                  <Select
                    value={addInstrumentForm.type}
                    label="Instrument Type"
                    onChange={(e) => handleFormChange('type', e.target.value)}
                  >
                    <MenuItem value="US_STOCK">US Stock</MenuItem>
                    <MenuItem value="HK_STOCK">Hong Kong Stock</MenuItem>
                    <MenuItem value="JP_STOCK">Japan Stock</MenuItem>
                    <MenuItem value="CRYPTO">Cryptocurrency</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Company Name"
                  placeholder="e.g., Apple Inc."
                  value={addInstrumentForm.name}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  error={!!formErrors.name}
                  helperText={formErrors.name || 'Full company or instrument name'}
                  required
                />
              </Grid>

              {/* Optional Fields */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                  Optional Information
                </Typography>
                <Divider sx={{ mb: 2 }} />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Market Cap"
                  placeholder="e.g., 2500000000000"
                  type="number"
                  value={addInstrumentForm.marketCap || ''}
                  onChange={(e) => handleFormChange('marketCap', e.target.value ? parseFloat(e.target.value) : undefined)}
                  error={!!formErrors.marketCap}
                  helperText={formErrors.marketCap || 'Market capitalization in USD'}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="IPO Year"
                  placeholder="e.g., 1980"
                  type="number"
                  value={addInstrumentForm.ipoYear || ''}
                  onChange={(e) => handleFormChange('ipoYear', e.target.value ? parseInt(e.target.value) : undefined)}
                  error={!!formErrors.ipoYear}
                  helperText={formErrors.ipoYear || 'Year of initial public offering'}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Country"
                  placeholder="e.g., United States"
                  value={addInstrumentForm.country || ''}
                  onChange={(e) => handleFormChange('country', e.target.value)}
                  helperText="Country of incorporation"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Sector"
                  placeholder="e.g., Technology"
                  value={addInstrumentForm.sector || ''}
                  onChange={(e) => handleFormChange('sector', e.target.value)}
                  helperText="Business sector"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Industry"
                  placeholder="e.g., Consumer Electronics"
                  value={addInstrumentForm.industry || ''}
                  onChange={(e) => handleFormChange('industry', e.target.value)}
                  helperText="Industry classification"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseAddDialog}
            disabled={addInstrumentLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleCreateInstrument}
            variant="contained"
            disabled={addInstrumentLoading}
            startIcon={addInstrumentLoading ? <CircularProgress size={20} /> : <AddIcon />}
          >
            {addInstrumentLoading ? 'Creating...' : 'Create Instrument'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Instrument Dialog */}
      <Dialog
        open={showEditDialog}
        onClose={handleCloseEditDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Edit Financial Instrument: {editingInstrument?.symbol}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              {/* Required Fields */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Required Information
                </Typography>
                <Divider sx={{ mb: 2 }} />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Symbol"
                  placeholder="e.g., AAPL"
                  value={editInstrumentForm.symbol}
                  onChange={(e) => handleEditFormChange('symbol', e.target.value.toUpperCase())}
                  error={!!formErrors.symbol}
                  helperText={formErrors.symbol || 'Unique instrument symbol (uppercase letters, numbers, dots, hyphens)'}
                  required
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Instrument Type</InputLabel>
                  <Select
                    value={editInstrumentForm.type}
                    label="Instrument Type"
                    onChange={(e) => handleEditFormChange('type', e.target.value)}
                  >
                    <MenuItem value="US_STOCK">US Stock</MenuItem>
                    <MenuItem value="HK_STOCK">Hong Kong Stock</MenuItem>
                    <MenuItem value="JP_STOCK">Japan Stock</MenuItem>
                    <MenuItem value="CRYPTO">Cryptocurrency</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Company Name"
                  placeholder="e.g., Apple Inc."
                  value={editInstrumentForm.name}
                  onChange={(e) => handleEditFormChange('name', e.target.value)}
                  error={!!formErrors.name}
                  helperText={formErrors.name || 'Full company or instrument name'}
                  required
                />
              </Grid>

              {/* Optional Fields */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                  Optional Information
                </Typography>
                <Divider sx={{ mb: 2 }} />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Market Cap"
                  placeholder="e.g., 2500000000000"
                  type="number"
                  value={editInstrumentForm.marketCap || ''}
                  onChange={(e) => handleEditFormChange('marketCap', e.target.value ? parseFloat(e.target.value) : undefined)}
                  error={!!formErrors.marketCap}
                  helperText={formErrors.marketCap || 'Market capitalization in USD'}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="IPO Year"
                  placeholder="e.g., 1980"
                  type="number"
                  value={editInstrumentForm.ipoYear || ''}
                  onChange={(e) => handleEditFormChange('ipoYear', e.target.value ? parseInt(e.target.value) : undefined)}
                  error={!!formErrors.ipoYear}
                  helperText={formErrors.ipoYear || 'Year of initial public offering'}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Country"
                  placeholder="e.g., United States"
                  value={editInstrumentForm.country || ''}
                  onChange={(e) => handleEditFormChange('country', e.target.value)}
                  helperText="Country of incorporation"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Sector"
                  placeholder="e.g., Technology"
                  value={editInstrumentForm.sector || ''}
                  onChange={(e) => handleEditFormChange('sector', e.target.value)}
                  helperText="Business sector"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Industry"
                  placeholder="e.g., Consumer Electronics"
                  value={editInstrumentForm.industry || ''}
                  onChange={(e) => handleEditFormChange('industry', e.target.value)}
                  helperText="Industry classification"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseEditDialog}
            disabled={editInstrumentLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpdateInstrument}
            variant="contained"
            disabled={editInstrumentLoading}
            startIcon={editInstrumentLoading ? <CircularProgress size={20} /> : <EditIcon />}
          >
            {editInstrumentLoading ? 'Updating...' : 'Update Instrument'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={showDeleteDialog}
        onClose={handleCloseDeleteDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <DeleteIcon color="error" />
            Confirm Deletion
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Are you sure you want to delete the following instrument?
          </Typography>

          {deletingInstrument && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="h6" color="primary" gutterBottom>
                {deletingInstrument.symbol}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {deletingInstrument.name}
              </Typography>
              <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                Type: {deletingInstrument.type} | Country: {deletingInstrument.country || 'N/A'}
              </Typography>
            </Box>
          )}

          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Warning:</strong> This action will permanently delete the instrument and all its associated data including:
            </Typography>
            <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
              <li>OHLCV historical data</li>
              <li>Technical indicators</li>
              <li>Position records</li>
              <li>Watchlist entries</li>
            </ul>
            <Typography variant="body2">
              This action cannot be undone.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseDeleteDialog}
            disabled={deleteInstrumentLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteInstrumentLoading}
            startIcon={deleteInstrumentLoading ? <CircularProgress size={20} /> : <DeleteIcon />}
          >
            {deleteInstrumentLoading ? 'Deleting...' : 'Delete Instrument'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Sync US Stock Data Confirmation Dialog */}
      <Dialog
        open={showSyncConfirmDialog}
        onClose={handleCloseSyncDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <SyncIcon color="primary" />
            Sync US Stock Data
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            This will synchronize your database with the latest SEC company ticker data.
          </Typography>

          <Alert severity="info" sx={{ mt: 2, mb: 2 }}>
            <Typography variant="body2">
              <strong>What this does:</strong>
            </Typography>
            <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
              <li>Downloads the latest SEC company ticker data</li>
              <li>Identifies new financial instruments not yet in your database</li>
              <li>Adds up to 1,000 new instruments in this operation</li>
              <li>Preserves all existing data and instruments</li>
            </ul>
          </Alert>

          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Note:</strong> This operation may take a few moments to complete and will add new instruments to your database.
              You can monitor the progress and see how many instruments were added after completion.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseSyncDialog}
            disabled={syncLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSyncUSStockData}
            variant="contained"
            color="primary"
            disabled={syncLoading}
            startIcon={syncLoading ? <CircularProgress size={20} /> : <SyncIcon />}
          >
            {syncLoading ? 'Syncing...' : 'Start Sync'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* CSV Upload Confirmation Dialog */}
      <Dialog
        open={showCsvUploadDialog}
        onClose={handleCloseCsvUploadDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <CloudUploadIcon color="primary" />
            Confirm CSV Upload
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            You are about to upload the following CSV file to update instrument data:
          </Typography>

          {selectedFile && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="h6" color="primary" gutterBottom>
                {selectedFile.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Size: {(selectedFile.size / 1024).toFixed(2)} KB
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Type: {selectedFile.type || 'text/csv'}
              </Typography>
            </Box>
          )}

          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>CSV Upload Process:</strong>
            </Typography>
            <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
              <li>Updates existing instrument records with new data from CSV</li>
              <li>Skips rows with symbols not found in the database</li>
              <li>Applies symbol normalization (^ to -P, / to -)</li>
              <li>Processes up to 1,000 instruments in this operation</li>
              <li>Validates data format and content before processing</li>
            </ul>
          </Alert>

          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Important:</strong> This operation will update existing instrument data in your database.
              The process may take several minutes for large files. Please ensure your CSV file follows the expected format.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseCsvUploadDialog}
            disabled={csvUploadLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleCsvUpload}
            variant="contained"
            color="primary"
            disabled={csvUploadLoading}
            startIcon={csvUploadLoading ? <CircularProgress size={20} /> : <UploadIcon />}
          >
            {csvUploadLoading ? 'Uploading...' : 'Upload CSV'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* CSV Upload Result Dialog */}
      <Dialog
        open={showCsvResultDialog}
        onClose={handleCloseCsvResultDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <CloudUploadIcon color="success" />
            CSV Upload Results
          </Box>
        </DialogTitle>
        <DialogContent>
          {csvUploadResult && (
            <>
              <Alert
                severity={csvUploadResult.addedInstruments > 0 || csvUploadResult.updatedInstruments > 0 ? 'success' : 'info'}
                sx={{ mb: 2 }}
              >
                {csvUploadResult.summary}
              </Alert>

              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center', py: 2 }}>
                      <Typography variant="h4" color="primary">
                        {csvUploadResult.totalRowsInCsv}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Rows
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center', py: 2 }}>
                      <Typography variant="h4" color="success.main">
                        {csvUploadResult.validRows}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Valid Rows
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center', py: 2 }}>
                      <Typography variant="h4" color="info.main">
                        {csvUploadResult.updatedInstruments}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Updated
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center', py: 2 }}>
                      <Typography variant="h4" color="warning.main">
                        {csvUploadResult.skippedInstruments}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Skipped
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {csvUploadResult.validationErrors.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="h6" color="error" gutterBottom>
                    Validation Errors:
                  </Typography>
                  <Box sx={{ maxHeight: 200, overflow: 'auto', bgcolor: 'grey.50', p: 1, borderRadius: 1 }}>
                    {csvUploadResult.validationErrors.map((error, index) => (
                      <Typography key={index} variant="body2" color="error" sx={{ mb: 0.5 }}>
                        • {error}
                      </Typography>
                    ))}
                  </Box>
                </Box>
              )}

              {csvUploadResult.processedSymbols.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    Processed Symbols ({csvUploadResult.processedSymbols.length}):
                  </Typography>
                  <Box sx={{ maxHeight: 150, overflow: 'auto', bgcolor: 'grey.50', p: 1, borderRadius: 1 }}>
                    <Typography variant="body2">
                      {csvUploadResult.processedSymbols.join(', ')}
                    </Typography>
                  </Box>
                </Box>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseCsvResultDialog}
            variant="contained"
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Instruments;
